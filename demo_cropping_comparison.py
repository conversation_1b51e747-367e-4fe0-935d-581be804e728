#!/usr/bin/env python3
"""
Demonstration script showing before/after comparison of the intelligent cropping system.
This script creates visual examples of how the new cropping system improves image quality.
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import numpy as np

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_comparison_demo():
    """Create a visual demonstration of the cropping improvements"""
    
    print("🎨 Creating Intelligent Cropping Demonstration")
    print("=" * 50)
    
    # Create a simulated "before" image (messy eBird screenshot)
    before_img = create_messy_ebird_screenshot()
    before_path = "demo_before_cropping.png"
    before_img.save(before_path)
    print(f"✅ Created 'before' example: {before_path}")
    
    # Create a simulated "after" image (clean cropped result)
    after_img = create_clean_cropped_result()
    after_path = "demo_after_cropping.png"
    after_img.save(after_path)
    print(f"✅ Created 'after' example: {after_path}")
    
    # Create a side-by-side comparison
    comparison_img = create_side_by_side_comparison(before_img, after_img)
    comparison_path = "demo_cropping_comparison.png"
    comparison_img.save(comparison_path)
    print(f"✅ Created comparison image: {comparison_path}")
    
    # Create an infographic showing the process
    process_img = create_process_infographic()
    process_path = "demo_cropping_process.png"
    process_img.save(process_path)
    print(f"✅ Created process infographic: {process_path}")
    
    print("\n🎯 Demonstration files created successfully!")
    print("View the generated images to see the cropping improvements.")

def create_messy_ebird_screenshot():
    """Create a simulated messy eBird screenshot with UI elements"""
    
    width, height = 1400, 900
    img = Image.new('RGB', (width, height), color=(240, 240, 240))
    draw = ImageDraw.Draw(img)
    
    # Add eBird-style UI elements
    
    # Top navigation bar
    draw.rectangle([0, 0, width, 80], fill=(45, 45, 45))
    draw.text((20, 25), "eBird", fill=(255, 255, 255))
    draw.text((100, 25), "Explore | Submit | Manage", fill=(200, 200, 200))
    
    # Left sidebar
    draw.rectangle([0, 80, 200, height], fill=(60, 60, 60))
    draw.text((20, 100), "Filters", fill=(255, 255, 255))
    draw.text((20, 130), "□ Photos", fill=(200, 200, 200))
    draw.text((20, 160), "□ Audio", fill=(200, 200, 200))
    draw.text((20, 190), "□ Video", fill=(200, 200, 200))
    
    # Right sidebar
    draw.rectangle([width-150, 80, width, height], fill=(70, 70, 70))
    draw.text((width-140, 100), "Info Panel", fill=(255, 255, 255))
    
    # Bottom controls
    draw.rectangle([0, height-60, width, height], fill=(50, 50, 50))
    draw.text((220, height-40), "◀ Previous | Next ▶", fill=(200, 200, 200))
    
    # Add a bird image in the center (but surrounded by UI clutter)
    bird_area = create_bird_image(400, 300)
    
    # Position the bird off-center with UI elements around it
    bird_x = 350
    bird_y = 200
    
    # Paste the bird image
    img.paste(bird_area, (bird_x, bird_y))
    
    # Add more UI clutter around the bird
    draw.rectangle([bird_x-20, bird_y-20, bird_x+420, bird_y-5], fill=(100, 100, 100))
    draw.text((bird_x, bird_y-18), "📷 Photo by John Doe | 📅 Dec 2024", fill=(255, 255, 255))
    
    draw.rectangle([bird_x-20, bird_y+305, bird_x+420, bird_y+340], fill=(90, 90, 90))
    draw.text((bird_x, bird_y+315), "⭐ Rate | 💬 Comment | 🔗 Share", fill=(255, 255, 255))
    
    return img

def create_clean_cropped_result():
    """Create a clean, professionally cropped bird image"""
    
    # Create a clean bird image with proper padding
    bird_img = create_bird_image(500, 400)
    
    # Add subtle padding around the bird
    padded_width = bird_img.width + 60
    padded_height = bird_img.height + 60
    
    clean_img = Image.new('RGB', (padded_width, padded_height), color=(245, 245, 240))
    clean_img.paste(bird_img, (30, 30))
    
    return clean_img

def create_bird_image(width, height):
    """Create a realistic-looking bird image"""
    
    img = Image.new('RGB', (width, height), color=(220, 200, 180))
    draw = ImageDraw.Draw(img)
    
    # Create a bird silhouette
    center_x, center_y = width // 2, height // 2
    
    # Bird body (oval)
    body_width, body_height = width // 3, height // 2
    draw.ellipse([
        center_x - body_width, center_y - body_height//2,
        center_x + body_width, center_y + body_height//2
    ], fill=(139, 69, 19))  # Brown
    
    # Bird head (circle)
    head_radius = body_height // 3
    head_x = center_x - body_width//2
    head_y = center_y - body_height//3
    draw.ellipse([
        head_x - head_radius, head_y - head_radius,
        head_x + head_radius, head_y + head_radius
    ], fill=(101, 67, 33))  # Darker brown
    
    # Beak
    beak_points = [
        (head_x - head_radius - 15, head_y),
        (head_x - head_radius, head_y - 8),
        (head_x - head_radius, head_y + 8)
    ]
    draw.polygon(beak_points, fill=(255, 140, 0))  # Orange beak
    
    # Eye
    eye_x = head_x - head_radius//3
    eye_y = head_y - head_radius//3
    draw.ellipse([eye_x-5, eye_y-5, eye_x+5, eye_y+5], fill=(0, 0, 0))
    draw.ellipse([eye_x-2, eye_y-2, eye_x+2, eye_y+2], fill=(255, 255, 255))
    
    # Wing details
    wing_points = [
        (center_x - body_width//2, center_y - body_height//4),
        (center_x + body_width//2, center_y - body_height//3),
        (center_x + body_width, center_y),
        (center_x + body_width//2, center_y + body_height//4)
    ]
    draw.polygon(wing_points, fill=(160, 82, 45))  # Lighter brown
    
    # Tail
    tail_points = [
        (center_x + body_width, center_y - body_height//4),
        (center_x + body_width + 40, center_y - body_height//2),
        (center_x + body_width + 50, center_y),
        (center_x + body_width + 40, center_y + body_height//2),
        (center_x + body_width, center_y + body_height//4)
    ]
    draw.polygon(tail_points, fill=(101, 67, 33))
    
    # Legs
    leg1_x = center_x - body_width//4
    leg2_x = center_x + body_width//4
    leg_y = center_y + body_height//2
    
    draw.line([leg1_x, leg_y, leg1_x, leg_y + 30], fill=(255, 140, 0), width=3)
    draw.line([leg2_x, leg_y, leg2_x, leg_y + 30], fill=(255, 140, 0), width=3)
    
    # Add some texture/noise for realism
    img_array = np.array(img)
    noise = np.random.randint(-10, 10, img_array.shape, dtype=np.int16)
    img_array = np.clip(img_array.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    return Image.fromarray(img_array)

def create_side_by_side_comparison(before_img, after_img):
    """Create a side-by-side comparison image"""
    
    # Resize images to same height for comparison
    target_height = 600
    
    before_ratio = before_img.width / before_img.height
    after_ratio = after_img.width / after_img.height
    
    before_resized = before_img.resize((int(target_height * before_ratio), target_height))
    after_resized = after_img.resize((int(target_height * after_ratio), target_height))
    
    # Create comparison canvas
    total_width = before_resized.width + after_resized.width + 100  # 100px gap
    total_height = target_height + 120  # Extra space for labels
    
    comparison = Image.new('RGB', (total_width, total_height), color=(250, 250, 250))
    draw = ImageDraw.Draw(comparison)
    
    # Add title
    draw.text((total_width//2 - 150, 20), "Intelligent Cropping Comparison", 
              fill=(50, 50, 50))
    
    # Paste images
    comparison.paste(before_resized, (20, 80))
    comparison.paste(after_resized, (before_resized.width + 80, 80))
    
    # Add labels
    draw.text((before_resized.width//2 - 30, 50), "BEFORE", fill=(200, 50, 50))
    draw.text((before_resized.width + 80 + after_resized.width//2 - 25, 50), "AFTER", 
              fill=(50, 150, 50))
    
    # Add descriptions
    draw.text((20, target_height + 90), "❌ Messy UI elements", fill=(150, 150, 150))
    draw.text((20, target_height + 105), "❌ Poor composition", fill=(150, 150, 150))
    
    draw.text((before_resized.width + 80, target_height + 90), "✅ Clean boundaries", 
              fill=(150, 150, 150))
    draw.text((before_resized.width + 80, target_height + 105), "✅ Professional crop", 
              fill=(150, 150, 150))
    
    return comparison

def create_process_infographic():
    """Create an infographic showing the cropping process"""
    
    width, height = 1200, 800
    img = Image.new('RGB', (width, height), color=(248, 248, 248))
    draw = ImageDraw.Draw(img)
    
    # Title
    draw.text((width//2 - 200, 30), "Intelligent Cropping Process", fill=(50, 50, 50))
    
    # Process steps
    steps = [
        ("1. Edge Detection", "Multi-scale Canny edge detection\nwith morphological operations"),
        ("2. Color Segmentation", "HSV color analysis for\nbird-specific color ranges"),
        ("3. UI Removal", "eBird-aware UI element\ndetection and removal"),
        ("4. Content Analysis", "Gradient-based interest\nmapping and texture detection"),
        ("5. Quality Assessment", "Multi-metric scoring:\nEdges, Contrast, Size, Ratio"),
        ("6. Enhancement", "Post-processing:\nSharpening, Noise reduction")
    ]
    
    step_width = width // 3
    step_height = height // 3
    
    for i, (title, description) in enumerate(steps):
        row = i // 3
        col = i % 3
        
        x = col * step_width + 50
        y = row * step_height + 120
        
        # Draw step box
        draw.rectangle([x, y, x + step_width - 100, y + step_height - 80], 
                      outline=(100, 100, 100), width=2)
        
        # Step title
        draw.text((x + 20, y + 20), title, fill=(50, 100, 150))
        
        # Step description
        lines = description.split('\n')
        for j, line in enumerate(lines):
            draw.text((x + 20, y + 50 + j * 20), line, fill=(100, 100, 100))
        
        # Arrow to next step (except for last step)
        if i < len(steps) - 1:
            if col == 2:  # End of row, arrow down
                draw.text((x + step_width//2, y + step_height - 60), "↓", fill=(150, 150, 150))
            else:  # Arrow right
                draw.text((x + step_width - 80, y + step_height//2), "→", fill=(150, 150, 150))
    
    # Result
    draw.text((width//2 - 100, height - 80), "🎯 Result: Professional Bird Images", 
              fill=(50, 150, 50))
    
    return img

if __name__ == "__main__":
    print("🚀 Starting Cropping Demonstration")
    create_comparison_demo()
    print("\n✨ Demonstration complete!")
    print("Check the generated images to see the improvements.")
