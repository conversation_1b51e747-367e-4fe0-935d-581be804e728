#!/usr/bin/env python3
"""
Test script for the new intelligent edge detection and dynamic cropping functionality
in the eBird scraper's screenshot system.

This script demonstrates the advanced cropping capabilities including:
- Smart edge detection with morphological operations
- Color-based segmentation for bird detection
- eBird UI-aware cropping
- Content-aware analysis using gradient detection
- Quality assessment and post-processing enhancement
"""

import os
import sys
import time
from PIL import Image
import cv2
import numpy as np

# Add the current directory to Python path to import scraperBot
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scraperBot import Scraper<PERSON>ot

def test_cropping_methods():
    """Test all the new intelligent cropping methods"""
    
    print("🧪 Testing Intelligent Bird Image Cropping System")
    print("=" * 60)
    
    # Initialize the scraper bot
    try:
        bot = ScraperBot()
        print("✅ ScraperBot initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize ScraperBot: {e}")
        return
    
    # Test with a sample image (you can replace this with an actual eBird screenshot)
    test_image_path = "test_screenshot.png"
    
    # Create a sample test image if it doesn't exist
    if not os.path.exists(test_image_path):
        print("📸 Creating sample test image...")
        create_sample_test_image(test_image_path)
    
    print(f"\n🔍 Testing cropping on: {test_image_path}")
    
    # Test the main cropping function
    try:
        result_path = bot._crop_screenshot_to_bird_image(test_image_path)
        
        if result_path and os.path.exists(result_path):
            print(f"✅ Cropping completed successfully!")
            print(f"📁 Result saved to: {result_path}")
            
            # Display image information
            with Image.open(result_path) as img:
                print(f"📊 Final image dimensions: {img.width}x{img.height}")
                print(f"📊 Image mode: {img.mode}")
        else:
            print("❌ Cropping failed or no result produced")
            
    except Exception as e:
        print(f"❌ Error during cropping test: {e}")
    
    # Test individual cropping methods
    print("\n🔬 Testing Individual Cropping Methods")
    print("-" * 40)
    
    try:
        with Image.open(test_image_path) as test_img:
            if test_img.mode != 'RGB':
                test_img = test_img.convert('RGB')
            
            # Test each method individually
            methods_to_test = [
                ("Boundary Detection", "_boundary_detection_crop"),
                ("Advanced Edge Detection", "_advanced_edge_detection_crop"),
                ("Color Segmentation", "_color_segmentation_crop"),
                ("eBird UI-Aware", "_ebird_ui_aware_crop"),
                ("Content-Aware", "_content_aware_crop"),
                ("Enhanced Center Crop", "_enhanced_center_crop")
            ]
            
            img_cv = cv2.cvtColor(np.array(test_img), cv2.COLOR_RGB2BGR)
            
            for method_name, method_func in methods_to_test:
                print(f"\n🧪 Testing {method_name}...")
                try:
                    if hasattr(bot, method_func):
                        if method_func == "_enhanced_center_crop":
                            result = getattr(bot, method_func)(test_img)
                        else:
                            result = getattr(bot, method_func)(img_cv, test_img)
                        
                        if result:
                            # Assess quality
                            quality = bot._assess_crop_quality(result, test_img)
                            print(f"   ✅ Success - Quality Score: {quality:.1f}")
                            print(f"   📊 Result: {result.width}x{result.height}")
                            
                            # Save individual result
                            method_filename = f"test_{method_func[1:]}.png"
                            result.save(method_filename)
                            print(f"   💾 Saved as: {method_filename}")
                        else:
                            print(f"   ❌ Method returned None")
                    else:
                        print(f"   ❌ Method {method_func} not found")
                        
                except Exception as e:
                    print(f"   ❌ Error: {e}")
    
    except Exception as e:
        print(f"❌ Error in individual method testing: {e}")
    
    print("\n🎯 Testing Complete!")
    print("=" * 60)

def create_sample_test_image(filename):
    """Create a sample test image that simulates an eBird screenshot"""
    
    # Create a 1200x800 image with a simulated eBird layout
    width, height = 1200, 800
    img = Image.new('RGB', (width, height), color='white')
    
    # Add some UI elements (dark bars for navigation)
    img_array = np.array(img)
    
    # Top navigation bar
    img_array[0:60, :] = [50, 50, 50]  # Dark gray
    
    # Bottom controls
    img_array[height-50:height, :] = [70, 70, 70]
    
    # Left sidebar
    img_array[:, 0:100] = [80, 80, 80]
    
    # Right sidebar
    img_array[:, width-80:width] = [80, 80, 80]
    
    # Add a simulated bird image in the center
    center_x, center_y = width // 2, height // 2
    bird_width, bird_height = 400, 300
    
    # Create a simple "bird" shape (brown oval)
    for y in range(center_y - bird_height//2, center_y + bird_height//2):
        for x in range(center_x - bird_width//2, center_x + bird_width//2):
            if 0 <= y < height and 0 <= x < width:
                # Create an oval shape
                dx = (x - center_x) / (bird_width // 2)
                dy = (y - center_y) / (bird_height // 2)
                if dx*dx + dy*dy <= 1:
                    # Brown color with some variation
                    brown_r = 139 + int(20 * np.sin(x * 0.1))
                    brown_g = 69 + int(15 * np.cos(y * 0.1))
                    brown_b = 19 + int(10 * np.sin((x+y) * 0.05))
                    img_array[y, x] = [brown_r, brown_g, brown_b]
    
    # Add some texture/noise to make it more realistic
    noise = np.random.randint(-20, 20, img_array.shape, dtype=np.int16)
    img_array = np.clip(img_array.astype(np.int16) + noise, 0, 255).astype(np.uint8)
    
    # Convert back to PIL and save
    final_img = Image.fromarray(img_array)
    final_img.save(filename)
    print(f"✅ Sample test image created: {filename}")

def demonstrate_quality_assessment():
    """Demonstrate the quality assessment functionality"""
    print("\n📊 Quality Assessment Demonstration")
    print("-" * 40)
    
    # This would typically be called with real cropped images
    # For demonstration, we'll show how the scoring works
    
    quality_factors = {
        "Edge Density": "Measures detail and sharpness - higher is better",
        "Contrast": "Measures subject definition - higher contrast = clearer bird",
        "Size Retention": "Larger crops preferred if they contain the subject",
        "Aspect Ratio": "Reasonable proportions (0.5-2.0 ratio is ideal)",
        "Center Bias": "Subjects near center are typically better composed"
    }
    
    print("🔍 Quality Assessment Metrics:")
    for metric, description in quality_factors.items():
        print(f"   • {metric}: {description}")
    
    print("\n💡 The system automatically selects the highest-scoring crop result!")

if __name__ == "__main__":
    print("🚀 Starting Intelligent Cropping Test Suite")
    print("This will test the new advanced bird image cropping system")
    print()
    
    # Run the main test
    test_cropping_methods()
    
    # Show quality assessment info
    demonstrate_quality_assessment()
    
    print("\n✨ Test suite completed!")
    print("Check the generated image files to see the cropping results.")
