# 📥 URL Download Feature Guide

## 🎯 Overview

Fitur URL Download telah diperbaiki dan sekarang dapat mendownload gambar langsung dari URL sumber, bukan menggunakan screenshot. Ini memberikan kualitas gambar yang lebih baik dan file size yang lebih kecil.

## 🔧 Perbaikan yang Dilakukan

### ✅ Masalah yang Diperbaiki:
1. **Hanya gambar pertama yang bisa didownload** - Sekarang semua gambar dapat didownload
2. **Navigasi kembali ke halaman utama** - Diperbaiki untuk memastikan scraper dapat melanjutkan ke gambar berikutnya
3. **URL detection yang tidak konsisten** - Ditingkatkan dengan multiple selectors dan scoring system
4. **Error handling** - Ditambahkan retry mechanism dan fallback ke screenshot

### 🆕 Fitur Baru:
- Parameter `download_method` untuk memilih antara 'url' atau 'screenshot'
- Improved URL quality scoring system
- Better error handling dan retry mechanism
- Progress tracking untuk URL downloads

## 🚀 Cara Penggunaan

### 1. Basic URL Download

```python
from scraperBot import EBirdScraperBot

# Initialize scraper
scraper = EBirdScraperBot(headless=False)

# Download menggunakan URL method
result = scraper.scrape_ebird(
    ebird_url="https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo",
    output_dir="my_bird_photos",
    max_images=10,
    method='click_and_view',
    download_method='url'  # 🔑 KEY: Gunakan URL download
)

scraper.wd.quit()
```

### 2. Command Line Usage

```bash
# URL Download method
python scraperBot.py --mode ebird \
  --ebird_url "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo" \
  --output_dir "bird_photos" \
  --max_images 15 \
  --download_method url

# Screenshot method (default)
python scraperBot.py --mode ebird \
  --ebird_url "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo" \
  --output_dir "bird_photos" \
  --max_images 15 \
  --download_method screenshot
```

### 3. Advanced Configuration

```python
scraper = EBirdScraperBot(headless=True)

result = scraper.scrape_ebird(
    ebird_url="https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo",
    output_dir="high_quality_birds",
    max_images=20,
    method='click_and_view',
    timeout_minutes=20,
    max_load_more_clicks=10,
    download_method='url',      # URL download
    crop_to_bird=False         # Tidak crop (hanya untuk screenshot)
)
```

## 📊 Perbandingan Method

| Aspect | URL Download | Screenshot |
|--------|-------------|------------|
| **File Quality** | ⭐⭐⭐⭐⭐ Original resolution | ⭐⭐⭐ Screen resolution |
| **File Size** | ⭐⭐⭐⭐ Smaller (50-500KB) | ⭐⭐ Larger (1-5MB) |
| **Speed** | ⭐⭐⭐⭐ Faster download | ⭐⭐⭐ Slower processing |
| **Reliability** | ⭐⭐⭐ Depends on URL detection | ⭐⭐⭐⭐ More reliable |
| **File Format** | JPG (original) | PNG (screenshot) |

## 🔍 Technical Details

### URL Detection Process:
1. **Click thumbnail** - Membuka gambar dalam viewer
2. **Wait for load** - Menunggu gambar resolusi tinggi dimuat
3. **Find best URL** - Mencari URL dengan kualitas terbaik menggunakan scoring system
4. **Download image** - Download langsung dari URL sumber
5. **Return to gallery** - Kembali ke halaman utama untuk gambar berikutnya

### URL Quality Scoring:
- `original` in URL: +1000 points
- `full` in URL: +800 points  
- `xlarge` in URL: +600 points
- `2400` or `1920` in URL: +500 points
- `thumbnail` in URL: -800 points
- `small` in URL: -400 points

## 🛠️ Troubleshooting

### Masalah: Hanya gambar pertama yang didownload
**Solusi:** Pastikan menggunakan versi terbaru dengan perbaikan navigasi

```python
# Pastikan parameter ini diset dengan benar
download_method='url'  # Bukan 'screenshot'
method='click_and_view'  # Bukan method lain
```

### Masalah: URL tidak ditemukan
**Solusi:** Scraper akan otomatis fallback ke screenshot method

```python
# Untuk memaksa URL only (tanpa fallback):
result = scraper._click_and_download_images(
    max_images=10,
    download_method='url',
    output_dir='my_photos'
)
```

### Masalah: Download gagal
**Solusi:** Cek koneksi internet dan URL validity

```python
# Enable verbose logging
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📝 Example Scripts

### Test URL Download:
```bash
python test_url_download.py
```

### Example Usage:
```bash
python example_url_download.py
```

### Compare Methods:
```python
# Dalam example_url_download.py
compare_download_methods()
```

## 🎯 Best Practices

1. **Gunakan URL method untuk kualitas terbaik**
   ```python
   download_method='url'
   ```

2. **Set timeout yang cukup untuk URL detection**
   ```python
   timeout_minutes=15  # Minimal 10-15 menit
   ```

3. **Batasi jumlah gambar untuk testing**
   ```python
   max_images=5  # Test dengan sedikit gambar dulu
   ```

4. **Gunakan headless mode untuk batch processing**
   ```python
   scraper = EBirdScraperBot(headless=True)
   ```

5. **Monitor progress dengan verbose output**
   ```python
   # Output akan menunjukkan:
   # ✅ Found full resolution URL: https://...
   # ✅ Successfully downloaded image 1 from URL
   ```

## 🔄 Migration dari Screenshot ke URL

Jika sebelumnya menggunakan screenshot method:

```python
# SEBELUM (Screenshot)
result = scraper.scrape_ebird(
    ebird_url=url,
    output_dir=output_dir,
    max_images=10,
    method='click_and_view'
    # crop_to_bird=True  # Default screenshot behavior
)

# SESUDAH (URL Download)
result = scraper.scrape_ebird(
    ebird_url=url,
    output_dir=output_dir,
    max_images=10,
    method='click_and_view',
    download_method='url'  # 🆕 Tambahkan parameter ini
)
```

## 📈 Performance Tips

1. **Untuk download banyak gambar:**
   - Gunakan `headless=True`
   - Set `max_load_more_clicks` yang reasonable
   - Monitor disk space

2. **Untuk kualitas maksimal:**
   - Gunakan `download_method='url'`
   - Set `timeout_minutes` yang cukup
   - Pastikan koneksi internet stabil

3. **Untuk testing:**
   - Mulai dengan `max_images=3`
   - Gunakan `headless=False` untuk debugging
   - Check output directory setelah setiap run

## 🎉 Kesimpulan

Fitur URL download sekarang bekerja dengan baik dan dapat mendownload semua gambar, bukan hanya gambar pertama. Kualitas gambar lebih baik karena didownload langsung dari sumber, dan file size lebih kecil dibanding screenshot method.

**Rekomendasi:** Gunakan `download_method='url'` untuk hasil terbaik!
