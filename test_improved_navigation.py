#!/usr/bin/env python3
"""
Test script untuk menguji perbaikan navigasi dan download URL
- Prioritas tombol X untuk kembali ke gallery
- Fokus pada download URL via JavaScript
- Tidak ada screenshot method
"""

import os
import sys
import time
import traceback
from scraperBot import EBirdScraperBot

def test_x_button_navigation():
    """Test navigasi kembali ke gallery menggunakan tombol X"""
    print("\n" + "="*60)
    print("🔘 TEST 1: Navigasi dengan Tombol X")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)  # GUI mode untuk melihat proses
        
        # Test URL
        test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
        
        print(f"📍 Navigating to test URL...")
        scraper.wd.get(test_url)
        time.sleep(5)
        
        # Get clickable images
        clickable_images = scraper._get_clickable_images_from_page()
        if not clickable_images:
            print("❌ No clickable images found")
            return False
            
        print(f"✅ Found {len(clickable_images)} clickable images")
        
        # Click first image
        first_image = clickable_images[0]
        print(f"🖱️ Clicking first image...")
        
        try:
            if first_image['parent_link']:
                first_image['parent_link'].click()
            else:
                first_image['element'].click()
            print(f"✅ Image clicked successfully")
        except Exception as e:
            print(f"❌ Click failed: {e}")
            return False
        
        time.sleep(3)
        
        # Test close modal with X button priority
        print(f"🔘 Testing close modal with X button priority...")
        close_success = scraper._close_modal_or_overlay()
        
        if close_success:
            print(f"✅ Modal closed successfully!")
            
            # Verify we're back in gallery
            time.sleep(2)
            gallery_images = scraper._get_clickable_images_from_page()
            if gallery_images:
                print(f"✅ Successfully returned to gallery - found {len(gallery_images)} images")
                return True
            else:
                print(f"⚠️ Modal closed but gallery not ready")
                return False
        else:
            print(f"❌ Failed to close modal")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def test_javascript_download():
    """Test download URL menggunakan JavaScript"""
    print("\n" + "="*60)
    print("📥 TEST 2: Download URL via JavaScript")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
        scraper.wd.get(test_url)
        time.sleep(5)
        
        # Get clickable images
        clickable_images = scraper._get_clickable_images_from_page()
        if not clickable_images:
            print("❌ No clickable images found")
            return False
        
        # Click first image
        first_image = clickable_images[0]
        if first_image['parent_link']:
            first_image['parent_link'].click()
        else:
            first_image['element'].click()
        
        time.sleep(3)
        
        # Wait for viewer and get URL
        print(f"🔍 Waiting for image viewer and getting URL...")
        full_url = scraper._wait_for_new_image_and_get_url(first_image.get('src', ''), 0)
        
        if full_url:
            print(f"✅ Full resolution URL found: {full_url[:80]}...")
            
            # Test JavaScript download
            test_dir = "test_js_download"
            os.makedirs(test_dir, exist_ok=True)
            
            download_success = scraper._download_image(full_url, "test_image.jpg", test_dir)
            
            if download_success:
                print(f"✅ JavaScript download successful!")
                
                # Clean up
                try:
                    files = os.listdir(test_dir)
                    for file in files:
                        os.remove(os.path.join(test_dir, file))
                    os.rmdir(test_dir)
                except:
                    pass
                    
                return True
            else:
                print(f"❌ JavaScript download failed")
                return False
        else:
            print(f"❌ No full resolution URL found")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def test_url_only_scraping():
    """Test complete scraping process dengan URL-only mode"""
    print("\n" + "="*60)
    print("🦅 TEST 3: Complete URL-Only Scraping")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
        test_dir = "test_url_only_scraping"
        os.makedirs(test_dir, exist_ok=True)
        
        print(f"🦅 Testing complete URL-only scraping with 3 images...")
        
        # Test with URL download method only
        downloaded_count = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=test_dir,
            max_images=3,
            method='click_and_view',
            download_method='url',  # URL only
            timeout_minutes=10
        )
        
        print(f"📊 Scraping completed. Downloaded: {downloaded_count} images")
        
        # Check results
        if downloaded_count > 0:
            files = os.listdir(test_dir)
            print(f"✅ Files created: {files}")
            
            # Verify file sizes
            for file in files:
                file_path = os.path.join(test_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"   📄 {file}: {file_size/1024:.1f} KB")
            
            # Clean up
            for file in files:
                try:
                    os.remove(os.path.join(test_dir, file))
                except:
                    pass
            try:
                os.rmdir(test_dir)
            except:
                pass
                
            return True
        else:
            print(f"❌ No images downloaded")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def test_navigation_reliability():
    """Test keandalan navigasi dengan multiple images"""
    print("\n" + "="*60)
    print("🔄 TEST 4: Navigation Reliability")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
        scraper.wd.get(test_url)
        time.sleep(5)
        
        # Get clickable images
        clickable_images = scraper._get_clickable_images_from_page()
        if len(clickable_images) < 3:
            print("❌ Need at least 3 images for this test")
            return False
        
        print(f"🔄 Testing navigation reliability with 3 images...")
        
        success_count = 0
        
        for i in range(3):
            print(f"\n--- Testing image {i+1}/3 ---")
            
            # Click image
            image = clickable_images[i]
            try:
                if image['parent_link']:
                    image['parent_link'].click()
                else:
                    image['element'].click()
                print(f"✅ Image {i+1} clicked")
            except Exception as e:
                print(f"❌ Failed to click image {i+1}: {e}")
                continue
            
            time.sleep(2)
            
            # Close modal and return to gallery
            close_success = scraper._close_modal_or_overlay()
            if close_success:
                print(f"✅ Modal closed for image {i+1}")
                
                # Verify gallery is ready
                time.sleep(2)
                gallery_images = scraper._get_clickable_images_from_page()
                if gallery_images:
                    print(f"✅ Gallery ready for next image ({len(gallery_images)} images)")
                    success_count += 1
                else:
                    print(f"❌ Gallery not ready after image {i+1}")
            else:
                print(f"❌ Failed to close modal for image {i+1}")
        
        print(f"\n📊 Navigation reliability: {success_count}/3 successful")
        
        if success_count >= 2:  # At least 2 out of 3 should work
            print(f"✅ Navigation reliability test PASSED")
            return True
        else:
            print(f"❌ Navigation reliability test FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def main():
    """Run all tests untuk validasi perbaikan"""
    print("🧪 IMPROVED NAVIGATION & URL DOWNLOAD TESTS")
    print("="*60)
    print("Testing perbaikan:")
    print("✓ Prioritas tombol X untuk navigasi")
    print("✓ Download URL via JavaScript")
    print("✓ Tidak ada screenshot method")
    print("✓ Keandalan navigasi")
    print("="*60)
    
    tests = [
        ("X Button Navigation", test_x_button_navigation),
        ("JavaScript Download", test_javascript_download),
        ("URL-Only Scraping", test_url_only_scraping),
        ("Navigation Reliability", test_navigation_reliability)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
            results[test_name] = False
        
        # Wait between tests
        time.sleep(3)
    
    # Print final results
    print("\n" + "="*60)
    print("🏁 FINAL TEST RESULTS")
    print("="*60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed >= 3:  # At least 3 out of 4 should pass
        print("🎉 TESTS MOSTLY PASSED! Perbaikan bekerja dengan baik.")
        return True
    else:
        print("⚠️ Some critical tests failed. Please review.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
