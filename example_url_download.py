#!/usr/bin/env python3
"""
Contoh penggunaan fitur URL download yang telah diperbaiki
"""

import os
from scraperBot import EBirdScraperBot

def download_bird_images_via_url():
    """Download gambar burung menggunakan URL download method"""
    print("🦅 eBird Image Downloader - URL Method")
    print("=" * 50)
    
    # Setup
    output_dir = "my_bird_photos_url"
    os.makedirs(output_dir, exist_ok=True)
    
    # URL eBird untuk burung tertentu
    # Contoh: Java Munia (Lonchura leucogastroides) di Indonesia
    ebird_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo&regionCode=ID"
    
    print(f"📍 Target URL: {ebird_url}")
    print(f"📁 Output directory: {output_dir}")
    print(f"📥 Method: URL Download (bukan screenshot)")
    
    # Initialize scraper
    scraper = EBirdScraperBot(headless=False)  # Set True untuk headless mode
    
    try:
        # Download gambar menggunakan URL method
        result = scraper.scrape_ebird(
            ebird_url=ebird_url,
            output_dir=output_dir,
            max_images=10,                    # Download maksimal 10 gambar
            method='click_and_view',          # Method: click dan lihat gambar penuh
            timeout_minutes=15,               # Timeout 15 menit
            max_load_more_clicks=5,           # Klik "Load More" maksimal 5 kali
            download_method='url'             # PENTING: Gunakan URL download
        )
        
        print(f"\n✅ Download selesai!")
        print(f"📊 Total gambar berhasil didownload: {result}")
        
        # Show downloaded files
        downloaded_files = [f for f in os.listdir(output_dir) if f.endswith('.jpg')]
        print(f"📁 File yang didownload:")
        
        for i, filename in enumerate(downloaded_files, 1):
            filepath = os.path.join(output_dir, filename)
            file_size = os.path.getsize(filepath) / 1024  # KB
            print(f"   {i}. {filename} ({file_size:.1f} KB)")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        # Cleanup
        try:
            scraper.wd.quit()
        except:
            pass

def download_multiple_species():
    """Download gambar dari beberapa spesies burung"""
    print("\n🦅 Multiple Species Download - URL Method")
    print("=" * 50)
    
    # Daftar spesies burung Indonesia
    bird_species = [
        {
            'name': 'Java Munia',
            'taxon_code': 'javmun1',
            'max_images': 5
        },
        {
            'name': 'Javan Kingfisher', 
            'taxon_code': 'javkin1',
            'max_images': 5
        },
        {
            'name': 'Javan Hawk-Eagle',
            'taxon_code': 'javhae1', 
            'max_images': 3
        }
    ]
    
    base_output_dir = "indonesian_birds_url"
    os.makedirs(base_output_dir, exist_ok=True)
    
    for species in bird_species:
        print(f"\n🐦 Downloading {species['name']}...")
        
        # Create species-specific directory
        species_dir = os.path.join(base_output_dir, species['name'].replace(' ', '_').lower())
        os.makedirs(species_dir, exist_ok=True)
        
        # Build eBird URL
        ebird_url = f"https://ebird.org/media/catalog?taxonCode={species['taxon_code']}&mediaType=photo&regionCode=ID"
        
        # Initialize scraper
        scraper = EBirdScraperBot(headless=True)  # Headless untuk batch processing
        
        try:
            result = scraper.scrape_ebird(
                ebird_url=ebird_url,
                output_dir=species_dir,
                max_images=species['max_images'],
                method='click_and_view',
                timeout_minutes=10,
                max_load_more_clicks=3,
                download_method='url'  # URL download method
            )
            
            print(f"   ✅ {species['name']}: {result} images downloaded")
            
        except Exception as e:
            print(f"   ❌ {species['name']}: Error - {e}")
            
        finally:
            try:
                scraper.wd.quit()
            except:
                pass

def compare_download_methods():
    """Bandingkan URL download vs Screenshot method"""
    print("\n🔍 Comparing Download Methods")
    print("=" * 50)
    
    test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo&regionCode=ID"
    
    methods = [
        {
            'name': 'URL Download',
            'method': 'url',
            'dir': 'comparison_url',
            'extension': '.jpg'
        },
        {
            'name': 'Screenshot',
            'method': 'screenshot', 
            'dir': 'comparison_screenshot',
            'extension': '.png'
        }
    ]
    
    results = {}
    
    for method_info in methods:
        print(f"\n📥 Testing {method_info['name']} method...")
        
        output_dir = method_info['dir']
        os.makedirs(output_dir, exist_ok=True)
        
        scraper = EBirdScraperBot(headless=True)
        
        try:
            start_time = time.time()
            
            result = scraper.scrape_ebird(
                ebird_url=test_url,
                output_dir=output_dir,
                max_images=3,
                method='click_and_view',
                timeout_minutes=8,
                max_load_more_clicks=2,
                download_method=method_info['method']
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Check files
            files = [f for f in os.listdir(output_dir) if f.endswith(method_info['extension'])]
            total_size = sum(os.path.getsize(os.path.join(output_dir, f)) for f in files) / 1024  # KB
            
            results[method_info['name']] = {
                'images': result,
                'files': len(files),
                'duration': duration,
                'total_size': total_size,
                'avg_size': total_size / len(files) if files else 0
            }
            
            print(f"   ✅ {method_info['name']}: {result} images, {duration:.1f}s")
            
        except Exception as e:
            print(f"   ❌ {method_info['name']}: Error - {e}")
            results[method_info['name']] = {'error': str(e)}
            
        finally:
            try:
                scraper.wd.quit()
            except:
                pass
    
    # Print comparison
    print(f"\n📊 Comparison Results:")
    print("-" * 50)
    for method_name, data in results.items():
        if 'error' not in data:
            print(f"{method_name}:")
            print(f"  Images: {data['images']}")
            print(f"  Files: {data['files']}")
            print(f"  Duration: {data['duration']:.1f}s")
            print(f"  Total size: {data['total_size']:.1f} KB")
            print(f"  Avg size: {data['avg_size']:.1f} KB")
        else:
            print(f"{method_name}: ERROR - {data['error']}")
        print()

if __name__ == "__main__":
    import time
    
    print("🚀 eBird URL Download Examples")
    print("=" * 60)
    
    # Example 1: Basic URL download
    download_bird_images_via_url()
    
    # Example 2: Multiple species (uncomment to run)
    # download_multiple_species()
    
    # Example 3: Method comparison (uncomment to run)
    # compare_download_methods()
    
    print("\n🏁 Examples completed!")
    print("💡 Tip: Ubah download_method='url' ke 'screenshot' untuk menggunakan screenshot method")
