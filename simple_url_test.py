#!/usr/bin/env python3
"""
Simple test untuk fitur URL download
"""

import os
from scraperBot import EBirdScraperBot

def simple_url_test():
    """Test sederhana untuk URL download"""
    print("🧪 Simple URL Download Test")
    print("=" * 40)
    
    # Setup
    output_dir = "simple_test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize scraper
    print("🚀 Initializing scraper...")
    scraper = EBirdScraperBot(headless=False)
    
    try:
        # Test URL - URL eBird yang sederhana
        test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo&regionCode=ID"
        
        print(f"🌐 Testing URL: {test_url}")
        print(f"📁 Output: {output_dir}")
        print(f"📥 Method: URL Download")
        print(f"🔢 Max images: 2 (untuk test)")
        
        # Test dengan URL download method
        print("\n🔄 Starting download...")
        result = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=output_dir,
            max_images=2,                    # Hanya 2 gambar untuk test
            method='click_and_view',
            timeout_minutes=5,               # Timeout singkat untuk test
            max_load_more_clicks=1,          # Hanya 1 kali load more
            download_method='url'            # 🔑 URL download method
        )
        
        print(f"\n📊 Test Results:")
        print(f"   Images processed: {result}")
        
        # Check files
        files = [f for f in os.listdir(output_dir) if f.endswith('.jpg')]
        print(f"   Files created: {len(files)}")
        
        for i, filename in enumerate(files, 1):
            filepath = os.path.join(output_dir, filename)
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath) / 1024  # KB
                print(f"   {i}. {filename} ({file_size:.1f} KB)")
            else:
                print(f"   {i}. {filename} (FILE NOT FOUND)")
        
        # Test result
        if len(files) > 0:
            print("\n✅ SUCCESS: URL Download working!")
            print("✅ Fitur download URL berhasil diperbaiki!")
            
            # Show first file info
            first_file = os.path.join(output_dir, files[0])
            if os.path.exists(first_file):
                size = os.path.getsize(first_file)
                print(f"✅ First file size: {size} bytes ({size/1024:.1f} KB)")
                
                # Check if it's a valid image
                try:
                    from PIL import Image
                    with Image.open(first_file) as img:
                        print(f"✅ Image dimensions: {img.size[0]}x{img.size[1]}")
                        print(f"✅ Image format: {img.format}")
                except Exception as e:
                    print(f"⚠️ Could not read image: {e}")
        else:
            print("\n❌ FAILED: No files downloaded")
            print("❌ URL download tidak berhasil")
            
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        try:
            scraper.wd.quit()
            print("✅ Browser closed")
        except:
            print("⚠️ Could not close browser")

if __name__ == "__main__":
    simple_url_test()
    print("\n🏁 Test completed!")
