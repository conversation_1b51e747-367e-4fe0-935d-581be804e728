#!/usr/bin/env python3
"""
Test script untuk menguji sistem deteksi gambar unik
- Memastikan setiap thumbnail menghasilkan gambar yang berbeda
- Menguji sistem tracking asset ID
- Validasi pencegahan duplikasi
"""

import os
import sys
import time
import traceback
from scraperBot import EBirdScraperBot

def test_asset_id_extraction():
    """Test ekstraksi asset ID dari berbagai format URL"""
    print("\n" + "="*60)
    print("🔍 TEST 1: Asset ID Extraction")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        # Test URLs dengan berbagai format
        test_urls = [
            "https://cdn.download.ams.birds.cornell.edu/api/v1/asset/638789348/2400",
            "https://macaulaylibrary.org/asset/638789348",
            "https://media.ebird.org/catalog/asset/638789348/full",
            "https://example.com/image/12345678/large.jpg",
            "https://invalid-url-no-asset-id.com/image.jpg"
        ]
        
        expected_ids = ["638789348", "638789348", "638789348", "12345678", None]
        
        success_count = 0
        
        for i, (url, expected) in enumerate(zip(test_urls, expected_ids)):
            print(f"\n--- Test URL {i+1} ---")
            print(f"URL: {url}")
            
            asset_id = scraper._extract_asset_id_fast(url)
            print(f"Extracted: {asset_id}")
            print(f"Expected: {expected}")
            
            if asset_id == expected:
                print(f"✅ PASS")
                success_count += 1
            else:
                print(f"❌ FAIL")
        
        print(f"\n📊 Asset ID Extraction: {success_count}/{len(test_urls)} tests passed")
        
        scraper.close()
        return success_count == len(test_urls)
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False

def test_unique_image_validation():
    """Test sistem validasi gambar unik"""
    print("\n" + "="*60)
    print("🎯 TEST 2: Unique Image Validation")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        # Test asset ID tracking
        test_asset_ids = ["638789348", "123456789", "987654321", "638789348"]  # Duplicate
        
        print("Testing asset ID uniqueness validation...")
        
        results = []
        for i, asset_id in enumerate(test_asset_ids):
            print(f"\n--- Testing Asset ID: {asset_id} ---")
            
            is_unique = scraper._is_asset_id_unique(asset_id)
            print(f"Is unique: {is_unique}")
            
            if is_unique:
                scraper._mark_asset_as_downloaded(asset_id)
                print(f"Marked as downloaded")
            
            results.append(is_unique)
        
        # Expected: True, True, True, False (last one is duplicate)
        expected = [True, True, True, False]
        
        success = results == expected
        print(f"\n📊 Results: {results}")
        print(f"📊 Expected: {expected}")
        print(f"📊 Unique Validation: {'PASS' if success else 'FAIL'}")
        
        # Print tracking stats
        scraper._print_asset_tracking_stats()
        
        scraper.close()
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False

def test_fast_image_detection():
    """Test deteksi gambar cepat di halaman eBird"""
    print("\n" + "="*60)
    print("🚀 TEST 3: Fast Image Detection")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
        
        print(f"📍 Navigating to test URL...")
        scraper.wd.get(test_url)
        time.sleep(5)
        
        # Get clickable images
        clickable_images = scraper._get_clickable_images_from_page()
        if not clickable_images:
            print("❌ No clickable images found")
            return False
        
        print(f"✅ Found {len(clickable_images)} clickable images")
        
        # Test clicking first image and fast detection
        first_image = clickable_images[0]
        print(f"🖱️ Clicking first image for fast detection test...")
        
        try:
            if first_image['parent_link']:
                first_image['parent_link'].click()
            else:
                first_image['element'].click()
            print(f"✅ Image clicked successfully")
        except Exception as e:
            print(f"❌ Click failed: {e}")
            return False
        
        time.sleep(3)
        
        # Test fast asset ID detection
        print(f"🔍 Testing fast asset ID detection...")
        start_time = time.time()
        
        asset_id = scraper._get_current_image_asset_id()
        
        detection_time = time.time() - start_time
        print(f"⏱️ Detection time: {detection_time:.2f} seconds")
        
        if asset_id:
            print(f"✅ Fast detection successful: Asset ID {asset_id}")
            
            # Test fast full resolution URL detection
            print(f"🚀 Testing fast full resolution URL detection...")
            start_time = time.time()
            
            full_url = scraper._find_full_resolution_image_fast()
            
            url_detection_time = time.time() - start_time
            print(f"⏱️ URL detection time: {url_detection_time:.2f} seconds")
            
            if full_url:
                print(f"✅ Fast URL detection successful: {full_url[:80]}...")
                
                # Verify asset ID matches
                url_asset_id = scraper._extract_asset_id_fast(full_url)
                if url_asset_id == asset_id:
                    print(f"✅ Asset ID verification successful: {asset_id}")
                    return True
                else:
                    print(f"❌ Asset ID mismatch: {url_asset_id} vs {asset_id}")
                    return False
            else:
                print(f"❌ Fast URL detection failed")
                return False
        else:
            print(f"❌ Fast asset ID detection failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def test_duplicate_prevention_in_action():
    """Test pencegahan duplikasi dalam aksi nyata"""
    print("\n" + "="*60)
    print("🛡️ TEST 4: Duplicate Prevention in Action")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
        test_dir = "test_duplicate_prevention"
        os.makedirs(test_dir, exist_ok=True)
        
        print(f"🦅 Testing duplicate prevention with 5 images...")
        print(f"   (This will test if the same image is detected multiple times)")
        
        # Run scraping with duplicate detection
        downloaded_count = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=test_dir,
            max_images=5,
            method='click_and_view',
            download_method='url',
            timeout_minutes=10
        )
        
        print(f"\n📊 Scraping completed. Downloaded: {downloaded_count} images")
        
        # Check results
        files = os.listdir(test_dir)
        print(f"📁 Files created: {len(files)}")
        
        # Analyze filenames for asset IDs
        asset_ids_in_files = []
        for file in files:
            if 'asset_' in file:
                try:
                    asset_id = file.split('asset_')[1].split('.')[0]
                    asset_ids_in_files.append(asset_id)
                except:
                    pass
        
        unique_assets = len(set(asset_ids_in_files))
        total_files = len(asset_ids_in_files)
        
        print(f"📊 Asset IDs in filenames: {asset_ids_in_files}")
        print(f"📊 Unique assets: {unique_assets}")
        print(f"📊 Total files: {total_files}")
        
        # Clean up
        for file in files:
            try:
                os.remove(os.path.join(test_dir, file))
            except:
                pass
        try:
            os.rmdir(test_dir)
        except:
            pass
        
        # Success if we have unique assets (no duplicates in files)
        success = unique_assets == total_files and downloaded_count > 0
        
        if success:
            print(f"✅ Duplicate prevention test PASSED")
        else:
            print(f"❌ Duplicate prevention test FAILED")
            
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def main():
    """Run all tests untuk validasi sistem deteksi gambar unik"""
    print("🧪 UNIQUE IMAGE DETECTION TESTS")
    print("="*60)
    print("Testing sistem deteksi gambar unik:")
    print("✓ Asset ID extraction dari URL")
    print("✓ Unique image validation system")
    print("✓ Fast image detection speed")
    print("✓ Duplicate prevention in action")
    print("="*60)
    
    tests = [
        ("Asset ID Extraction", test_asset_id_extraction),
        ("Unique Image Validation", test_unique_image_validation),
        ("Fast Image Detection", test_fast_image_detection),
        ("Duplicate Prevention", test_duplicate_prevention_in_action)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
            results[test_name] = False
        
        # Wait between tests
        time.sleep(3)
    
    # Print final results
    print("\n" + "="*60)
    print("🏁 FINAL TEST RESULTS")
    print("="*60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed >= 3:  # At least 3 out of 4 should pass
        print("🎉 UNIQUE IMAGE DETECTION WORKING! No more duplicate downloads.")
        return True
    else:
        print("⚠️ Some critical tests failed. Duplicate detection needs improvement.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
