#!/usr/bin/env python3
"""
Test script untuk menguji fitur URL download yang telah diperbaiki
"""

import os
import time
from scraperBot import EBirdScraperBot

def test_url_download():
    """Test URL download functionality"""
    print("🧪 Testing URL Download Functionality")
    print("=" * 50)
    
    # Setup
    output_dir = "test_url_downloads"
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize scraper
    scraper = EBirdScraperBot(headless=False)
    
    try:
        # Test URL - ganti dengan URL eBird yang sesuai
        test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo&regionCode=ID"
        
        print(f"🌐 Testing URL: {test_url}")
        print(f"📁 Output directory: {output_dir}")
        print(f"📥 Download method: URL")
        
        # Test scraping dengan URL download method
        result = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=output_dir,
            max_images=3,  # Test dengan 3 gambar saja
            method='click_and_view',
            timeout_minutes=10,
            max_load_more_clicks=2,
            download_method='url'  # Gunakan URL download
        )
        
        print(f"\n📊 Test Results:")
        print(f"   Images processed: {result}")
        
        # Check downloaded files
        downloaded_files = [f for f in os.listdir(output_dir) if f.endswith('.jpg')]
        print(f"   Files downloaded: {len(downloaded_files)}")
        
        for i, filename in enumerate(downloaded_files, 1):
            filepath = os.path.join(output_dir, filename)
            file_size = os.path.getsize(filepath) / 1024  # KB
            print(f"   {i}. {filename} ({file_size:.1f} KB)")
        
        if len(downloaded_files) > 0:
            print("✅ URL Download test PASSED!")
            print("✅ Fitur download URL berhasil diperbaiki!")
        else:
            print("❌ URL Download test FAILED!")
            print("❌ Tidak ada file yang berhasil didownload")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        
    finally:
        # Cleanup
        try:
            scraper.wd.quit()
        except:
            pass

def test_screenshot_comparison():
    """Test perbandingan antara URL download dan screenshot"""
    print("\n🧪 Testing URL vs Screenshot Comparison")
    print("=" * 50)
    
    # Setup directories
    url_dir = "test_url_method"
    screenshot_dir = "test_screenshot_method"
    os.makedirs(url_dir, exist_ok=True)
    os.makedirs(screenshot_dir, exist_ok=True)
    
    test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo&regionCode=ID"
    
    # Test URL method
    print("📥 Testing URL download method...")
    scraper1 = EBirdScraperBot(headless=False)
    try:
        result1 = scraper1.scrape_ebird(
            ebird_url=test_url,
            output_dir=url_dir,
            max_images=2,
            method='click_and_view',
            timeout_minutes=5,
            max_load_more_clicks=1,
            download_method='url'
        )
        print(f"URL method result: {result1}")
    except Exception as e:
        print(f"URL method error: {e}")
        result1 = 0
    finally:
        try:
            scraper1.wd.quit()
        except:
            pass
    
    time.sleep(3)  # Wait between tests
    
    # Test Screenshot method
    print("\n📸 Testing screenshot method...")
    scraper2 = EBirdScraperBot(headless=False)
    try:
        result2 = scraper2.scrape_ebird(
            ebird_url=test_url,
            output_dir=screenshot_dir,
            max_images=2,
            method='click_and_view',
            timeout_minutes=5,
            max_load_more_clicks=1,
            download_method='screenshot'
        )
        print(f"Screenshot method result: {result2}")
    except Exception as e:
        print(f"Screenshot method error: {e}")
        result2 = 0
    finally:
        try:
            scraper2.wd.quit()
        except:
            pass
    
    # Compare results
    print(f"\n📊 Comparison Results:")
    print(f"   URL downloads: {result1}")
    print(f"   Screenshots: {result2}")
    
    # Check file sizes
    url_files = [f for f in os.listdir(url_dir) if f.endswith('.jpg')]
    screenshot_files = [f for f in os.listdir(screenshot_dir) if f.endswith('.png')]
    
    print(f"   URL files: {len(url_files)}")
    print(f"   Screenshot files: {len(screenshot_files)}")
    
    if len(url_files) > 0:
        avg_url_size = sum(os.path.getsize(os.path.join(url_dir, f)) for f in url_files) / len(url_files) / 1024
        print(f"   Average URL file size: {avg_url_size:.1f} KB")
    
    if len(screenshot_files) > 0:
        avg_screenshot_size = sum(os.path.getsize(os.path.join(screenshot_dir, f)) for f in screenshot_files) / len(screenshot_files) / 1024
        print(f"   Average screenshot file size: {avg_screenshot_size:.1f} KB")

if __name__ == "__main__":
    print("🚀 Starting URL Download Tests")
    print("=" * 60)
    
    # Test 1: Basic URL download
    test_url_download()
    
    # Test 2: Comparison
    test_screenshot_comparison()
    
    print("\n🏁 All tests completed!")
    print("=" * 60)
