#!/usr/bin/env python3
"""
Example script showing how to use the new image cropping functionality
"""

from scraperBot import EBirdScraperBot
import os

def example_with_cropping():
    """Example: eBird scraping with automatic bird image cropping"""
    print("🦅 EBIRD SCRAPING WITH AUTOMATIC BIRD CROPPING")
    print("=" * 60)
    
    # Create scraper instance
    scraper = EBirdScraperBot(headless=False)  # Set to True for headless mode
    
    # Example eBird URL (Java Sparrow in Indonesia)
    ebird_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    
    # Output directory
    output_dir = "ebird_cropped_images"
    
    try:
        print(f"📍 URL: {ebird_url}")
        print(f"📁 Output: {output_dir}")
        print(f"✂️ Cropping: ENABLED (will crop to bird image only)")
        print("=" * 60)
        
        # Scrape with cropping enabled (default)
        result = scraper.scrape_ebird(
            ebird_url=ebird_url,
            output_dir=output_dir,
            max_images=5,  # Just 5 images for demo
            method='click_and_view',
            timeout_minutes=10,
            crop_to_bird=True  # Enable cropping
        )
        
        print(f"\n✅ Scraping completed!")
        print(f"📊 Images processed: {result}")
        print(f"📁 Check folder: {output_dir}")
        print(f"✂️ All screenshots have been cropped to focus on bird images only!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def example_without_cropping():
    """Example: eBird scraping without cropping (full screenshots)"""
    print("\n🦅 EBIRD SCRAPING WITHOUT CROPPING (FULL SCREENSHOTS)")
    print("=" * 60)
    
    # Create scraper instance
    scraper = EBirdScraperBot(headless=False)
    
    # Example eBird URL
    ebird_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
    
    # Output directory
    output_dir = "ebird_full_screenshots"
    
    try:
        print(f"📍 URL: {ebird_url}")
        print(f"📁 Output: {output_dir}")
        print(f"📸 Cropping: DISABLED (will keep full page screenshots)")
        print("=" * 60)
        
        # Scrape without cropping
        result = scraper.scrape_ebird(
            ebird_url=ebird_url,
            output_dir=output_dir,
            max_images=3,  # Just 3 images for demo
            method='click_and_view',
            timeout_minutes=10,
            crop_to_bird=False  # Disable cropping
        )
        
        print(f"\n✅ Scraping completed!")
        print(f"📊 Images processed: {result}")
        print(f"📁 Check folder: {output_dir}")
        print(f"📸 All screenshots are full page (not cropped)")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def command_line_examples():
    """Show command line usage examples"""
    print("\n💻 COMMAND LINE USAGE EXAMPLES")
    print("=" * 60)
    
    print("🔹 With cropping (default):")
    print('python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --max_images 10 --out_directory "./cropped_birds"')
    
    print("\n🔹 Without cropping:")
    print('python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --max_images 10 --out_directory "./full_screenshots" --no_crop')
    
    print("\n🔹 With ultra quality and cropping:")
    print('python scraperBot.py --mode ebird --ebird_url "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1" --max_images 5 --ultra_quality --out_directory "./ultra_quality_cropped"')
    
    print("\n📋 Cropping Options:")
    print("  --crop_to_bird    : Enable cropping (default)")
    print("  --no_crop         : Disable cropping, keep full screenshots")

def cropping_benefits():
    """Explain the benefits of cropping"""
    print("\n✨ BENEFITS OF AUTOMATIC BIRD IMAGE CROPPING")
    print("=" * 60)
    
    print("✅ FOCUSED CONTENT:")
    print("   • Removes website headers, footers, and navigation")
    print("   • Focuses on the actual bird image")
    print("   • Eliminates distracting UI elements")
    
    print("\n✅ BETTER QUALITY:")
    print("   • Bird image takes up more of the frame")
    print("   • Higher effective resolution for the bird")
    print("   • Better for training AI models or analysis")
    
    print("\n✅ STORAGE EFFICIENCY:")
    print("   • Smaller file sizes (less background)")
    print("   • More relevant content per pixel")
    print("   • Easier to organize and browse")
    
    print("\n✅ MULTIPLE CROPPING STRATEGIES:")
    print("   • Content detection (finds main image area)")
    print("   • UI removal (removes common webpage elements)")
    print("   • Smart center crop (focuses on center area)")
    print("   • Automatic fallback if one method fails")

if __name__ == "__main__":
    print("🔧 EBIRD SCRAPER WITH AUTOMATIC BIRD IMAGE CROPPING")
    print("=" * 70)
    
    # Show benefits
    cropping_benefits()
    
    # Show command line examples
    command_line_examples()
    
    # Ask user what they want to do
    print("\n🤔 What would you like to do?")
    print("1. Run example with cropping")
    print("2. Run example without cropping")
    print("3. Just show examples (no actual scraping)")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            example_with_cropping()
        elif choice == "2":
            example_without_cropping()
        elif choice == "3":
            print("\n📋 Examples shown above. Use the command line examples to run the scraper.")
        else:
            print("Invalid choice. Showing examples only.")
            
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    
    print("\n📋 EXAMPLE COMPLETE")
    print("💡 TIP: Use --no_crop flag to disable cropping if you want full screenshots")
