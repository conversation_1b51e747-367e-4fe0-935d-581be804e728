#!/usr/bin/env python3
"""
Test script to validate the infinite loop fixes in eBird scraper
Tests all the critical fixes implemented to prevent infinite loops and ensure reliable operation
"""

import os
import sys
import time
import traceback
from scraperBot import EBirdScraperBot

def test_image_viewer_detection():
    """Test the new image viewer detection logic"""
    print("\n" + "="*60)
    print("🔍 TEST 1: Image Viewer Detection Logic")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)  # GUI mode to see what's happening
        
        # Test URL with known images
        test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
        
        print(f"📍 Navigating to test URL...")
        scraper.wd.get(test_url)
        time.sleep(5)
        
        # Get first clickable image
        clickable_images = scraper._get_clickable_images_from_page()
        if not clickable_images:
            print("❌ No clickable images found")
            return False
            
        print(f"✅ Found {len(clickable_images)} clickable images")
        
        # Test clicking first image and viewer detection
        first_image = clickable_images[0]
        print(f"🖱️ Testing click and viewer detection on first image...")
        
        # Click the image
        try:
            if first_image['parent_link']:
                first_image['parent_link'].click()
            else:
                first_image['element'].click()
            print(f"✅ Image clicked successfully")
        except Exception as e:
            print(f"❌ Click failed: {e}")
            return False
        
        # Test progressive wait for viewer
        print(f"⏳ Testing progressive wait for viewer...")
        viewer_detected = scraper._progressive_wait_for_viewer(0)
        
        if viewer_detected:
            print(f"✅ Image viewer detected successfully!")
            
            # Test finding full resolution image
            full_url = scraper._find_full_resolution_image()
            if full_url:
                print(f"✅ Full resolution URL found: {full_url[:80]}...")
                return True
            else:
                print(f"⚠️ Viewer detected but no full resolution URL found")
                return True  # Still a success for viewer detection
        else:
            print(f"❌ Image viewer detection failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def test_cache_clearing_and_refresh():
    """Test the browser cache clearing and refresh mechanisms"""
    print("\n" + "="*60)
    print("🧹 TEST 2: Cache Clearing and Refresh Mechanisms")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
        scraper.wd.get(test_url)
        time.sleep(3)
        
        print(f"🧹 Testing cache clearing and refresh...")
        
        # Test cache clearing method
        scraper._clear_browser_cache_and_refresh()
        
        # Verify page is still functional after cache clear
        time.sleep(3)
        clickable_images = scraper._get_clickable_images_from_page()
        
        if clickable_images:
            print(f"✅ Cache clearing successful - page still functional with {len(clickable_images)} images")
            return True
        else:
            print(f"❌ Cache clearing may have broken page functionality")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def test_error_handling_and_recovery():
    """Test the comprehensive error handling and recovery mechanisms"""
    print("\n" + "="*60)
    print("🚨 TEST 3: Error Handling and Recovery")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        # Initialize error tracker
        scraper._error_tracker = {
            'consecutive_failures': 0,
            'total_failures': 0,
            'last_error_time': 0,
            'error_types': {}
        }
        
        # Test standard error recovery
        print(f"🔧 Testing standard error recovery...")
        recovery_success = scraper._standard_error_recovery()
        print(f"Standard recovery result: {recovery_success}")
        
        # Test enhanced error recovery
        print(f"🔧 Testing enhanced error recovery...")
        recovery_success = scraper._enhanced_error_recovery()
        print(f"Enhanced recovery result: {recovery_success}")
        
        # Test error handling with simulated error
        print(f"🚨 Testing error handling with simulated error...")
        test_error = Exception("Simulated test error")
        stats = {'failed': 0, 'errors': []}
        
        recovery_success = scraper._handle_processing_error(0, test_error, stats)
        print(f"Error handling result: {recovery_success}")
        print(f"Error tracker state: {scraper._error_tracker}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def test_screenshot_quality_validation():
    """Test the improved screenshot quality validation"""
    print("\n" + "="*60)
    print("📸 TEST 4: Screenshot Quality Validation")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
        scraper.wd.get(test_url)
        time.sleep(5)
        
        # Get first clickable image
        clickable_images = scraper._get_clickable_images_from_page()
        if not clickable_images:
            print("❌ No clickable images found")
            return False
        
        # Click first image
        first_image = clickable_images[0]
        if first_image['parent_link']:
            first_image['parent_link'].click()
        else:
            first_image['element'].click()
        
        time.sleep(3)
        
        # Test screenshot with quality validation
        print(f"📸 Testing screenshot with quality validation...")
        test_dir = "test_screenshots"
        os.makedirs(test_dir, exist_ok=True)
        
        screenshot_path = scraper._take_screenshot(0, test_dir, crop_to_bird=False)
        
        if screenshot_path and os.path.exists(screenshot_path):
            file_size = os.path.getsize(screenshot_path)
            print(f"✅ Screenshot successful: {os.path.basename(screenshot_path)}")
            print(f"📊 File size: {file_size/1024:.1f} KB")
            
            # Clean up test file
            try:
                os.remove(screenshot_path)
                os.rmdir(test_dir)
            except:
                pass
                
            return True
        else:
            print(f"❌ Screenshot failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def test_full_scraping_process():
    """Test the complete scraping process with all fixes"""
    print("\n" + "="*60)
    print("🦅 TEST 5: Complete Scraping Process")
    print("="*60)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        test_url = "https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1"
        test_dir = "test_full_scraping"
        os.makedirs(test_dir, exist_ok=True)
        
        print(f"🦅 Testing complete scraping process with 3 images...")
        
        # Test with URL download method (should be more reliable now)
        downloaded_count = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=test_dir,
            max_images=3,
            method='click_and_view',
            download_method='url',  # Test URL method
            timeout_minutes=10
        )
        
        print(f"📊 Scraping completed. Downloaded: {downloaded_count} images")
        
        # Check results
        if downloaded_count > 0:
            files = os.listdir(test_dir)
            print(f"✅ Files created: {files}")
            
            # Clean up
            for file in files:
                try:
                    os.remove(os.path.join(test_dir, file))
                except:
                    pass
            try:
                os.rmdir(test_dir)
            except:
                pass
                
            return True
        else:
            print(f"❌ No images downloaded")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False
    finally:
        try:
            scraper.close()
        except:
            pass

def main():
    """Run all tests to validate the infinite loop fixes"""
    print("🧪 INFINITE LOOP FIXES VALIDATION TESTS")
    print("="*60)
    print("Testing all critical fixes implemented to prevent infinite loops")
    print("and ensure reliable eBird scraper operation")
    print("="*60)
    
    tests = [
        ("Image Viewer Detection", test_image_viewer_detection),
        ("Cache Clearing & Refresh", test_cache_clearing_and_refresh),
        ("Error Handling & Recovery", test_error_handling_and_recovery),
        ("Screenshot Quality", test_screenshot_quality_validation),
        ("Complete Scraping Process", test_full_scraping_process)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"💥 {test_name}: ERROR - {e}")
            results[test_name] = False
        
        # Wait between tests
        time.sleep(2)
    
    # Print final results
    print("\n" + "="*60)
    print("🏁 FINAL TEST RESULTS")
    print("="*60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Infinite loop fixes are working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the fixes.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
