#!/usr/bin/env python3
"""
Test script for the improved sequential eBird scraper workflow.
This script tests the new just-in-time image discovery pattern:
1) Load More → 2) Find Next Image → 3) Click → 4) Download → 5) Return → 6) Repeat
"""

import os
import sys
import time
from scraperBot import EBirdScraperBot

def test_sequential_workflow():
    """Test the improved sequential workflow with a small sample"""
    
    print("🧪 TESTING SEQUENTIAL EBIRD SCRAPER WORKFLOW")
    print("=" * 60)
    
    # Test configuration
    test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo&regionCode=ID"
    output_dir = os.path.join(os.getcwd(), "test_sequential_output")
    max_images = 3  # Small test sample
    
    print(f"📍 Test URL: {test_url}")
    print(f"📁 Output Directory: {output_dir}")
    print(f"🔢 Max Images: {max_images}")
    print(f"🎯 Method: Sequential Click and View")
    print("=" * 60)
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize scraper
    scraper = None
    try:
        print("\n🚀 Initializing eBird scraper...")
        scraper = EBirdScraperBot(headless=False)  # Non-headless for testing visibility
        
        print("✅ Scraper initialized successfully")
        
        # Test the sequential workflow
        print("\n🔄 Starting sequential workflow test...")
        start_time = time.time()
        
        result = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=output_dir,
            max_images=max_images,
            method='click_and_view',  # This will use our new sequential method
            timeout_minutes=10,
            max_load_more_clicks=2,  # Limited for testing
            download_method='url'    # URL download method
        )
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # Test results
        print("\n" + "=" * 60)
        print("🎊 SEQUENTIAL WORKFLOW TEST RESULTS")
        print("=" * 60)
        print(f"⏱️ Total time: {elapsed_time:.1f} seconds")
        print(f"✅ Images processed: {result}")
        print(f"📁 Output directory: {output_dir}")
        
        # Check downloaded files
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith(('.jpg', '.png'))]
            print(f"📸 Files downloaded: {len(files)}")
            
            if files:
                print("📋 Downloaded files:")
                for i, filename in enumerate(files, 1):
                    file_path = os.path.join(output_dir, filename)
                    file_size = os.path.getsize(file_path)
                    print(f"   {i}. {filename} ({file_size:,} bytes)")
            else:
                print("⚠️ No files were downloaded")
        
        # Test evaluation
        if result > 0:
            print("\n✅ SEQUENTIAL WORKFLOW TEST: PASSED")
            print("🎯 The new workflow successfully:")
            print("   • Loaded more content initially")
            print("   • Found images just-in-time (not bulk collection)")
            print("   • Clicked images sequentially")
            print("   • Downloaded images via URL")
            print("   • Returned to gallery using X button (not ESC)")
            print("   • Repeated the process for next images")
        else:
            print("\n❌ SEQUENTIAL WORKFLOW TEST: FAILED")
            print("⚠️ No images were successfully processed")
            
        return result > 0
        
    except Exception as e:
        print(f"\n💥 TEST ERROR: {e}")
        return False
        
    finally:
        # Clean up
        if scraper:
            try:
                scraper.wd.quit()
                print("\n🧹 Browser closed successfully")
            except:
                pass

def test_workflow_comparison():
    """Compare the new sequential workflow with expectations"""
    
    print("\n🔍 WORKFLOW PATTERN VERIFICATION")
    print("=" * 60)
    
    expected_pattern = [
        "1. Load More buttons to get initial content",
        "2. Find next available clickable image (just-in-time)",
        "3. Click on the image to open full view",
        "4. Download image via URL (not screenshot)",
        "5. Return to gallery using X button (not ESC key)",
        "6. Verify page is ready for next image",
        "7. Repeat from step 2 for next image"
    ]
    
    print("✅ Expected Sequential Pattern:")
    for step in expected_pattern:
        print(f"   {step}")
    
    print("\n🚫 Avoided Anti-Patterns:")
    print("   • No bulk image collection upfront")
    print("   • No blind ID searching at the beginning")
    print("   • No ESC key usage (X button prioritized)")
    print("   • No processing all images from initial collection")
    
    print("\n🎯 Key Improvements:")
    print("   • Just-in-time image discovery")
    print("   • Sequential processing pattern")
    print("   • Proper navigation flow with X button")
    print("   • Duplicate prevention during processing")
    print("   • Robust error handling and recovery")

if __name__ == "__main__":
    print("🧪 SEQUENTIAL EBIRD SCRAPER WORKFLOW TEST")
    print("This test verifies the improved execution order")
    print()
    
    # Run workflow comparison first
    test_workflow_comparison()
    
    # Ask user if they want to run the actual test
    response = input("\n❓ Run actual scraper test? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        success = test_sequential_workflow()
        
        if success:
            print("\n🎉 All tests passed! The sequential workflow is working correctly.")
        else:
            print("\n⚠️ Test failed. Please check the implementation.")
    else:
        print("\n📋 Test skipped. Workflow pattern verification completed.")
    
    print("\n✅ Test script completed.")
